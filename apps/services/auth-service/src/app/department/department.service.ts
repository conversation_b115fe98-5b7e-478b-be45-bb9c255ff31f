import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Department } from '../user/model/department.model';
import { Organization } from '../organization/organization.model';
import { User } from '../user/model/user.model';
import { UserDepartment } from '../user/model/user‐department.model';

export interface CreateDepartmentRequest {
  name: string;
  organizationId: number;
  parentId?: number;
  description?: string;
}

export interface UpdateDepartmentRequest {
  id: number;
  name?: string;
  parentId?: number;
  description?: string;
}

export interface DepartmentResponse {
  success: boolean;
  message: string;
  department?: any;
}

export interface ListDepartmentsRequest {
  organizationId: number;
  parentId?: number;
  includeChildren?: boolean;
  page?: number;
  limit?: number;
}

export interface ListDepartmentsResponse {
  success: boolean;
  message: string;
  departments: any[];
  total: number;
  page: number;
  limit: number;
}

export interface GetDepartmentRequest {
  id: number;
}

export interface DeleteDepartmentRequest {
  id: number;
  getArgs: {
    userId?: number;
    roleName?: string;
    ipAddress?: string;
    userAgent?: string;
  };
}

export interface DeleteDepartmentResponse {
  success: boolean;
  message: string;
}

export interface GetOrganizationDepartmentsRequest {
  organizationId: number;
  includeHierarchy?: boolean;
}

export interface GetOrganizationDepartmentsResponse {
  success: boolean;
  message: string;
  departments: any[];
}

export interface RenameOrgDepartmentRequest {
  id: number;
  departmentName: string;
  getArgs: {
    userId?: number;
    roleName?: string;
    ipAddress?: string;
    userAgent?: string;
  };
}

export interface RenameOrgDepartmentResponse {
  success: boolean;
  message: string;
}

@Injectable()
export class DepartmentService {
  private readonly logger = new Logger(DepartmentService.name);

  constructor(
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
    @InjectModel(Organization)
    private readonly organizationModel: typeof Organization,
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(UserDepartment)
    private readonly userDepartmentModel: typeof UserDepartment
  ) {}

  async createDepartment(
    request: CreateDepartmentRequest
  ): Promise<DepartmentResponse> {
    try {
      this.logger.log(
        `Creating department: ${request.name} for organization: ${request.organizationId}`
      );

      // Validate organization exists
      const organization = await this.organizationModel.findByPk(
        request.organizationId
      );
      if (!organization) {
        throw new BadRequestException(
          `Organization with ID ${request.organizationId} not found`
        );
      }

      // Validate parent department if provided
      if (request.parentId) {
        const parentDepartment = await this.departmentModel.findOne({
          where: {
            id: request.parentId,
            organizationId: request.organizationId, // Ensure parent is in same organization
          },
        });

        if (!parentDepartment) {
          throw new BadRequestException(
            `Parent department with ID ${request.parentId} not found in this organization`
          );
        }
      }

      // Check if department already exists with the same name and parent in this organization
      const existingDepartment = await this.departmentModel.findOne({
        where: {
          name: request.name,
          organizationId: request.organizationId,
          parentId: request.parentId || null,
        },
      });

      if (existingDepartment) {
        // Allow departments with same name but different parents
        if (request.parentId !== existingDepartment.parentId) {
          this.logger.log(
            `Department '${request.name}' exists with different parent, creating new instance`
          );
        } else {
          throw new BadRequestException(
            `Department '${request.name}' already exists with the same parent in this organization`
          );
        }
      }

      // Create department
      const department = await this.departmentModel.create({
        name: request.name,
        organizationId: request.organizationId,
        parentId: request.parentId,
      });

      return {
        success: true,
        message: 'Department created successfully',
        department: await this.getDepartmentWithDetails(Number(department.id)),
      };
    } catch (error) {
      this.logger.error(
        `Error creating department: ${error.message}`,
        error.stack
      );
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new Error('Failed to create department');
    }
  }

  async updateDepartment(
    request: UpdateDepartmentRequest
  ): Promise<DepartmentResponse> {
    try {
      this.logger.log(`Updating department: ${request.id}`);

      const department = await this.departmentModel.findByPk(request.id);
      if (!department) {
        throw new NotFoundException(
          `Department with ID ${request.id} not found`
        );
      }

      // Validate parent department if provided
      if (request.parentId) {
        const parentDepartment = await this.departmentModel.findOne({
          where: {
            id: request.parentId,
            organizationId: department.organizationId, // Ensure parent is in same organization
          },
        });

        if (!parentDepartment) {
          throw new BadRequestException(
            `Parent department with ID ${request.parentId} not found in this organization`
          );
        }

        // Prevent circular references
        if (
          await this.wouldCreateCircularReference(request.id, request.parentId)
        ) {
          throw new BadRequestException(
            'Cannot set parent department: would create circular reference'
          );
        }
      }

      // Update department fields
      if (request.name) department.name = request.name;
      if (request.parentId !== undefined)
        department.parentId = request.parentId;

      await department.save();

      return {
        success: true,
        message: 'Department updated successfully',
        department: await this.getDepartmentWithDetails(Number(department.id)),
      };
    } catch (error) {
      this.logger.error(
        `Error updating department: ${error.message}`,
        error.stack
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new Error('Failed to update department');
    }
  }

  async listDepartments(
    request: ListDepartmentsRequest
  ): Promise<ListDepartmentsResponse> {
    try {
      const page = request.page || 1;
      const limit = request.limit || 10;
      const offset = (page - 1) * limit;

      const whereClause: any = {
        organizationId: request.organizationId,
      };

      if (request.parentId !== undefined) {
        whereClause.parentId = request.parentId;
      }

      const includeOptions: any[] = [
        {
          model: Organization,
          as: 'organization',
          attributes: ['id', 'name', 'type'],
        },
      ];

      if (request.includeChildren) {
        includeOptions.push({
          model: Department,
          as: 'children',
          attributes: ['id', 'name', 'parentId'],
        });
      }

      const { rows: departments, count: total } =
        await this.departmentModel.findAndCountAll({
          where: whereClause,
          include: includeOptions,
          limit,
          offset,
          order: [['name', 'ASC']],
        });

      return {
        success: true,
        message: 'Departments retrieved successfully',
        departments: departments.map((dept) => this.formatDepartmentFlat(dept)),
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(
        `Error listing departments: ${error.message}`,
        error.stack
      );
      throw new Error('Failed to list departments');
    }
  }

  async getDepartmentById(id: number): Promise<any> {
    const department = await this.getDepartmentWithDetails(id);
    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }
    return department;
  }

  async deleteDepartment(
    req: DeleteDepartmentRequest
  ): Promise<DeleteDepartmentResponse> {
    try {
      const department = await this.departmentModel.findByPk(req.id);
      if (!department) {
        throw new NotFoundException(`Department with ID ${req.id} not found`);
      }

      // Check if department has children
      const childrenCount = await this.departmentModel.count({
        where: { parentId: req.id },
      });

      if (childrenCount > 0) {
        throw new BadRequestException(
          'Cannot delete department with child departments'
        );
      }

      // Check if department has users
      const usersCount = await this.userDepartmentModel.count({
        where: { departmentId: req.id },
      });

      if (usersCount > 0) {
        throw new BadRequestException(
          'Cannot delete department with assigned users'
        );
      }

      // Delete department
      await department.destroy();

      return {
        success: true,
        message: 'Department deleted successfully',
      };
    } catch (error) {
      this.logger.error(
        `Error deleting department: ${error.message}`,
        error.stack
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new Error('Failed to delete department');
    }
  }

  async renameDepartment(
    req: RenameOrgDepartmentRequest
  ): Promise<RenameOrgDepartmentResponse> {
    try {
      const department = await this.departmentModel.findByPk(req.id);
      if (!department) {
        throw new NotFoundException(`Department with ID ${req.id} not found`);
      }

      // Check if new name already exists in the same organization
      const existingDepartment = await this.departmentModel.findOne({
        where: {
          name: req.departmentName,
          organizationId: department.organizationId,
        },
      });

      if (existingDepartment) {
        throw new BadRequestException(
          `Department with name '${req.departmentName}' already exists in this organization`
        );
      }

      // Rename department
      department.name = req.departmentName;
      await department.save();

      return {
        success: true,
        message: 'Department renamed successfully',
      };
    } catch (error) {
      this.logger.error(
        `Error renaming department: ${error.message}`,
        error.stack
      );
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new Error('Failed to rename department');
    }
  }

  async getDepartmentsForOrganization(
    organizationId: number,
    includeHierarchy = false
  ): Promise<any[]> {
    // Always return flat list with numeric IDs and parent IDs
    const departments = await this.departmentModel.findAll({
      where: { organizationId },
      include: [
        {
          model: Organization,
          as: 'organization',
          attributes: ['id', 'name', 'type'],
        },
      ],
      order: [['name', 'ASC']],
    });

    return departments.map((dept) => this.formatDepartmentFlat(dept));
  }

  async getAllDepartments(includeHierarchy: boolean = true): Promise<any[]> {
    try {
      this.logger.log(
        `Fetching all departments across all organizations${
          includeHierarchy ? ' with hierarchy' : ''
        }`
      );

      const includeOptions = [
        {
          model: Organization,
          as: 'organization',
          attributes: ['id', 'name', 'type'],
        },
      ];

      // Only include parent and children if hierarchy is requested
      if (includeHierarchy) {
        includeOptions.push(
          {
            model: Department,
            as: 'parent',
            attributes: ['id', 'name'],
          } as any,
          {
            model: Department,
            as: 'children',
            attributes: ['id', 'name', 'parentId'],
          } as any
        );
      }

      const departments = await this.departmentModel.findAll({
        include: includeOptions,
        order: [['name', 'ASC']],
      });

      // Identify all child department IDs
      const childIds = new Set(
        departments
          .flatMap((dept) => dept.children?.map((child) => child.id) || [])
          .filter((id) => id !== undefined)
      );

      // Filter top-level departments (no parent or not a child)
      const topLevelDepartments = departments.filter(
        (dept) => !dept.parentId || !childIds.has(dept.id)
      );

      const formattedDepartments = topLevelDepartments.map((dept) =>
        this.formatDepartment(dept, true)
      );

      return formattedDepartments;
    } catch (error) {
      this.logger.error(
        `Error fetching all departments: ${error.message}`,
        error.stack
      );
      throw new Error('Failed to fetch all departments');
    }
  }

  private async getDepartmentWithDetails(id: number): Promise<any> {
    const department = await this.departmentModel.findByPk(id, {
      include: [
        {
          model: Organization,
          as: 'organization',
          attributes: ['id', 'name', 'type'],
        },
        {
          model: Department,
          as: 'parent',
          attributes: ['id', 'name'],
        },
        {
          model: Department,
          as: 'children',
          attributes: ['id', 'name', 'parentId'],
        },
      ],
    });

    return department ? this.formatDepartment(department) : null;
  }

  private formatDepartment(
    department: Department,
    includeHierarchy: boolean = true
  ): any {
    const formatted: any = {
      id: Number(department.id), // Serialize bigint to number
      name: department.name,
    };

    if (includeHierarchy) {
      formatted.parentId = department.parentId
        ? Number(department.parentId)
        : undefined;

      formatted.children = (department.children || []).map((child) =>
        this.formatDepartment(child, true)
      );
    }

    return formatted;
  }

  private formatDepartmentFlat(department: Department): any {
    return {
      id: Number(department.id), // Serialize bigint to number
      name: department.name,
      parentId: department.parentId ? Number(department.parentId) : null,
    };
  }

  private formatDepartmentWithChildren(deptData: any): any {
    const formatted: any = {
      id: Number(deptData.id),
      name: deptData.name,
    };

    if (deptData.parentId) {
      formatted.parentId = Number(deptData.parentId);
    }

    if (deptData.children && deptData.children.length > 0) {
      formatted.children = deptData.children.map((child) =>
        this.formatDepartmentWithChildren(child)
      );
    }

    return formatted;
  }

  private async wouldCreateCircularReference(
    departmentId: number,
    parentId: number
  ): Promise<boolean> {
    // Check if setting parentId would create a circular reference
    let currentParentId = parentId;
    const visited = new Set<number>();

    while (currentParentId) {
      if (visited.has(currentParentId) || currentParentId === departmentId) {
        return true;
      }
      visited.add(currentParentId);

      const parent = await this.departmentModel.findByPk(currentParentId);
      currentParentId = parent?.parentId || null;
    }

    return false;
  }
}
