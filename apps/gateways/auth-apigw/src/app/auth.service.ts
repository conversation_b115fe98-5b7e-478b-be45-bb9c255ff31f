import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import {
  Client,
  ClientGrpc,
  RpcException,
  Transport,
} from '@nestjs/microservices';
import { join } from 'path';
import { firstValueFrom, Observable, map, catchError, throwError } from 'rxjs';
import Long from 'long';
import {
  AssignDepartmentRequest,
  AssignDepartmentResponse,
  AuthService as AuthGrpcService,
  BulkCreateModulesRequest,
  BulkCreateModulesResponse,
  CreateDepartmentResponse,
  CreateModuleRequest,
  CreateModuleResponse,
  CreateRoleRequest,
  CreateRoleWithDetailsRequest,
  CreateRoleWithDetailsResponse,
  DeleteRoleRequest,
  DeleteRoleResponse,
  DepartmentInfo,
  GenerateOtpResponse,
  GetDataRequest,
  ListDepartmentsResponse,
  ListDepartmentsWithUsersResponse,
  ListModulesResponse,
  ListRolesRequest,
  ListRolesResponse,
  LogoutResponse,
  RefreshTokenResponse,
  RegisterRequest,
  RegisterResponse,
  RoleDetailsResponse,
  RoleResponse,
  RoleWithDetailsResponse,
  SsoAuthRequest,
  UpdateRoleRequest,
  UpdateRoleResponse,
  VerifyOtpResponse,
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
  GetEmployeeRequest,
  ListEmployeesRequest,
  RemoveEmployeeRequest,
  EmployeeResponse,
  ListEmployeesResponse,
  RemoveEmployeeResponse,
  CreateDepartmentRequest,
  UpdateOrgDepartmentRequest,
  GetOrgDepartmentRequest,
  DeleteOrgDepartmentRequest,
  GetOrganizationDepartmentsRequest,
  OrgDepartmentResponse,
  DeleteOrgDepartmentResponse,
  GetOrganizationDepartmentsResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
  VerifyResetTokenRequest,
  VerifyResetTokenResponse,
  GetRoleRequest,
  GetOrganizationRolesRequest,
  GetOrganizationRolesResponse,
  ListOrgDepartmentsRequest,
  ListOrgDepartmentsResponse,
  RenameOrgDepartmentRequest,
  RenameOrgDepartmentResponse,
} from './auth.interface';
import {
  serializeDepartmentIds,
  serializeDeptAndUserIds,
  serializeUserResponse,
  toNum,
  toNumber,
  serializeDepartments,
} from './utils/serializeResponse';
import { status } from '@grpc/grpc-js';

@Injectable()
export class AuthClientService implements OnModuleInit {
  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'auth',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/auth/auth.proto'),
      url: 'auth-service:50052',
    },
  })
  private readonly client: ClientGrpc;

  private authService: AuthGrpcService;

  onModuleInit() {
    this.authService = this.client.getService<AuthGrpcService>('AuthService');
  }

  // ─── Authentication ─────────────────────────────────────────────────
  // #region
  register(req: RegisterRequest): Observable<RegisterResponse> {
    return this.authService.register(req);
  }

  async login(
    email: string,
    password: string,
    ipAddress: string,
    userAgent?: string
  ) {
    const resp = await firstValueFrom(
      this.authService.login({ email, password, ipAddress, userAgent })
    );
    // Logger.debug(
    //   `Login successful for user: ${JSON.stringify(
    //     serializeUserResponse(resp.user),
    //     null,
    //     2
    //   )}`
    // );
    // const userLoginData = { ...resp, user: resp.user };
    return { ...resp, user: serializeUserResponse(resp.user) };
  }

  logout(token: string): Observable<LogoutResponse> {
    return this.authService.logout({ accessToken: token });
  }

  async validateToken(token: string) {
    const resp = await firstValueFrom(
      this.authService.validateToken({ token })
    );
    return { ...resp, user: serializeUserResponse(resp.user) };
  }

  refreshToken(refreshToken: string): Observable<RefreshTokenResponse> {
    return this.authService.refreshToken({ refreshToken });
  }

  async ssoAuth(req: SsoAuthRequest) {
    const resp = await firstValueFrom(this.authService.ssoAuth(req));
    return { ...resp, user: serializeUserResponse(resp.user) };
  }

  // ─── OTP ─────────────────────────────────────────────────────────────

  generateOtp(
    email: string,
    type: string,
    ipAddress?: string,
    userAgent?: string
  ): Observable<GenerateOtpResponse> {
    return this.authService.generateOtp({ email, type, ipAddress, userAgent });
  }

  verifyOtp(
    email: string,
    otp: string,
    type: string,
    ipAddress?: string,
    userAgent?: string
  ): Observable<VerifyOtpResponse> {
    return this.authService.verifyOtp({
      email,
      otp,
      type,
      ipAddress,
      userAgent,
    });
  }
  // #endregion

  // ─── User Management ─────────────────────────────────────────────────
  // #region

  // #endregion

  // ─── Role Management ─────────────────────────────────────────────────
  // #region
  createRoleWithDetails(
    data: CreateRoleWithDetailsRequest
  ): Observable<CreateRoleWithDetailsResponse> {
    Logger.debug(`createRoleWithDetails: ${JSON.stringify(data, null, 2)}`);
    return this.authService.createRoleWithDetails(data).pipe(
      map((resp) => {
        // helper to convert Long to number and map the complete response
        return {
          success: resp.success,
          message: resp.message,
          role: {
            id: toNum(resp.role.id),
            name: resp.role.name,
            description: resp.role.description,
            departments:
              resp.role.departments?.map((dept: any) => ({
                id: toNum(dept.id),
                name: dept.name,
                users:
                  dept.users?.map((user: any) => ({
                    id: toNum(user.id),
                    name: user.name,
                    email: user.email,
                  })) || [],
              })) || [],
            modules:
              resp.role.modules?.map((module: any) => ({
                id: toNum(module.id),
                module: module.module,
                features:
                  module.features?.map((feature: any) => ({
                    id: toNum(feature.id),
                    feature: feature.feature,
                    permissions: feature.permissions,
                    subFeatures:
                      feature.subFeatures?.map((subFeature: any) => ({
                        id: toNum(subFeature.id),
                        subFeature: subFeature.subFeature,
                        permissions: subFeature.permissions,
                      })) || [],
                  })) || [],
              })) || [],
          },
        };
      })
    );
  }

  getRolesWithDetails(
    getArgs: GetDataRequest
  ): Observable<RoleWithDetailsResponse> {
    return this.authService.getRolesWithDetails(getArgs).pipe(
      map((resp) => {
        const roles = (resp.roles || []).map((role) => {
          const normalizeId = (id: any) =>
            Long.isLong(id) ? id.toNumber() : id;

          const departments = (role.department || []).map((dep) => ({
            ...dep,
            id: normalizeId(dep.id),
            users: (dep.users || []).map((u) => ({
              ...u,
              id: normalizeId(u.id),
            })),
          }));

          const modules = (role.modules || []).map((mod) => ({
            ...mod,
            id: normalizeId(mod.id),
            features: (mod.features || []).map((feat) => ({
              ...feat,
              id: normalizeId(feat.id),
              subFeatures: (feat.subFeatures || []).map((sf) => ({
                ...sf,
                id: normalizeId(sf.id),
              })),
            })),
          }));

          return {
            ...role,
            id: normalizeId(role.id),
            department: departments,
            modules,
          };
        });

        return {
          ...resp,
          roles,
        };
      })
    );
  }

  getRoleDetails(
    name: string,
    getArgs: GetDataRequest
  ): Observable<RoleDetailsResponse> {
    return this.authService.getRoleDetails({ name, getArgs }).pipe(
      map((resp) => ({
        success: resp.success,
        message: resp.message,
        role: {
          id: toNum(resp.role.id),
          role: resp.role.role,
          department: resp.role.department?.map((d) => ({
            id: toNum(d.id),
            name: d.name,
            users: d.users?.map((u) => ({
              id: toNum(u.id),
              name: u.name,
              email: u.email,
            })),
          })),
          modules: resp.role.modules?.map((m) => ({
            id: toNum(m.id),
            module: m.module,
            features: m.features?.map((f) => ({
              id: toNum(f.id),
              feature: f.feature,
              permissions: f.permissions,
              subFeatures: f.subFeatures?.map((sf) => ({
                id: toNum(sf.id),
                subFeature: sf.subFeature,
                permissions: sf.permissions,
              })),
            })),
          })),
        },
      })),
      catchError((err) => {
        Logger.warn(`gRPC getRoleDetails failed: [${err.code}] ${err.message}`);
        // If it's a NOT_FOUND from the auth-service, re-throw it unchanged:
        if (err.code === status.NOT_FOUND) {
          return throwError(
            () =>
              new RpcException({
                code: status.NOT_FOUND,
                message: err.message,
                details: err.details,
              })
          );
        }
        // for anything else, bubble it up as a 500
        return throwError(() => new RpcException(err));
      })
    );
  }

  renameRole(data: UpdateRoleRequest): Observable<UpdateRoleResponse> {
    return this.authService.updateRole(data);
  }

  deleteRole(data: DeleteRoleRequest): Observable<DeleteRoleResponse> {
    return this.authService.deleteRole(data);
  }

  // #endregion

  // ─── User–Role & Permission ──────────────────────────────────────────
  // #region

  // #endregion

  // ─── Module & Feature ────────────────────────────────────────────────
  // #region
  createModule(data: CreateModuleRequest): Observable<CreateModuleResponse> {
    return this.authService.createModule(data);
  }

  /** bulk call */
  bulkCreateModules(
    data: BulkCreateModulesRequest
  ): Observable<BulkCreateModulesResponse> {
    return this.authService.bulkCreateModules(data);
  }

  listModules(getArgs: GetDataRequest): Observable<ListModulesResponse> {
    return this.authService.listModules(getArgs);
  }

  // #endregion

  // ─── Department ──────────────────────────────────────────────────────
  // #region
  createDepartment(
    data: CreateDepartmentRequest
  ): Observable<CreateDepartmentResponse> {
    // Logger.debug(`createDepartment: ${JSON.stringify(data, null, 2)}`);
    return this.authService.createDepartment(data);
  }

  assignDepartment(
    data: AssignDepartmentRequest
  ): Observable<AssignDepartmentResponse> {
    return this.authService.assignDepartment(data);
  }

  listDepartments(
    getArgs: GetDataRequest
  ): Observable<ListDepartmentsResponse> {
    return this.authService.listDepartments(getArgs).pipe(
      map((data: DepartmentInfo[] | ListDepartmentsResponse) => {
        // Check if data is DepartmentInfo[] (type mismatch case)
        if (Array.isArray(data)) {
          const departments = serializeDepartmentIds(data);
          return {
            success: true,
            message: 'Departments retrieved with hierarchy',
            departments,
          };
        }

        // Handle ListDepartmentsResponse
        if (!data || !data.departments) {
          return {
            success: false,
            message: 'Invalid response from auth service',
            departments: [],
          };
        }

        const serializedDepartments = serializeDepartmentIds(data.departments);
        return {
          ...data,
          message: data.message || 'Departments retrieved with hierarchy',
          departments: serializedDepartments,
        };
      })
    );
  }

  listDepartmentsWithUsers(
    getArgs: GetDataRequest
  ): Observable<ListDepartmentsWithUsersResponse> {
    return this.authService.listDepartmentsWithUsers(getArgs).pipe(
      map((resp: ListDepartmentsWithUsersResponse) => {
        // normalize every dept (and nested children + users) to real numbers
        // Logger.debug(
        //   `listDepartmentsWithUsers: ${JSON.stringify(resp, longReplacer, 2)}`
        // );
        const departments = (resp.departments || []).map(
          serializeDeptAndUserIds
        );
        return {
          success: resp.success,
          message: resp.message,
          departments,
        };
      })
    );
  }
  // #endregion

  // ─── Employee Management ───────────────────────────────────────────────
  // #region
  createEmployee(req: CreateEmployeeRequest): Observable<EmployeeResponse> {
    return this.authService.createEmployee(req).pipe(
      map((resp: EmployeeResponse) => ({
        ...resp,
        employee: resp.employee
          ? {
              ...resp.employee,
              userId: toNumber(resp.employee.userId),
              agencyId: resp.employee.agencyId
                ? toNumber(resp.employee.agencyId)
                : undefined,
              addresses: resp.employee.addresses?.map((addr) => ({
                ...addr,
                id: addr.id ? toNumber(addr.id) : undefined,
                userId: toNumber(addr.userId),
              })),
              emergencyContacts: resp.employee.emergencyContacts?.map(
                (contact) => ({
                  ...contact,
                  id: contact.id ? toNumber(contact.id) : undefined,
                  userId: toNumber(contact.userId),
                })
              ),
              identityDocs: resp.employee.identityDocs?.map((doc) => ({
                ...doc,
                id: doc.id ? toNumber(doc.id) : undefined,
                userId: toNumber(doc.userId),
              })),
              bankAccounts: resp.employee.bankAccounts?.map((account) => ({
                ...account,
                id: account.id ? toNumber(account.id) : undefined,
                userId: toNumber(account.userId),
              })),
            }
          : undefined,
      }))
    );
  }

  getEmployee(req: GetEmployeeRequest): Observable<EmployeeResponse> {
    return this.authService.getEmployee(req).pipe(
      map((resp: EmployeeResponse) => ({
        ...resp,
        employee: resp.employee
          ? {
              ...resp.employee,
              userId: toNumber(resp.employee.userId),
              agencyId: resp.employee.agencyId
                ? toNumber(resp.employee.agencyId)
                : undefined,
              addresses: resp.employee.addresses?.map((addr) => ({
                ...addr,
                id: addr.id ? toNumber(addr.id) : undefined,
                userId: toNumber(addr.userId),
              })),
              emergencyContacts: resp.employee.emergencyContacts?.map(
                (contact) => ({
                  ...contact,
                  id: contact.id ? toNumber(contact.id) : undefined,
                  userId: toNumber(contact.userId),
                })
              ),
              identityDocs: resp.employee.identityDocs?.map((doc) => ({
                ...doc,
                id: doc.id ? toNumber(doc.id) : undefined,
                userId: toNumber(doc.userId),
              })),
              bankAccounts: resp.employee.bankAccounts?.map((account) => ({
                ...account,
                id: account.id ? toNumber(account.id) : undefined,
                userId: toNumber(account.userId),
              })),
            }
          : undefined,
      }))
    );
  }

  listEmployees(req: ListEmployeesRequest): Observable<ListEmployeesResponse> {
    return this.authService.listEmployees(req).pipe(
      map((resp: ListEmployeesResponse) => ({
        ...resp,
        employees:
          resp.employees?.map((employee) => ({
            ...employee,
            userId: toNumber(employee.userId),
            agencyId: employee.agencyId
              ? toNumber(employee.agencyId)
              : undefined,
            addresses: employee.addresses?.map((addr) => ({
              ...addr,
              id: addr.id ? toNumber(addr.id) : undefined,
              userId: toNumber(addr.userId),
            })),
            emergencyContacts: employee.emergencyContacts?.map((contact) => ({
              ...contact,
              id: contact.id ? toNumber(contact.id) : undefined,
              userId: toNumber(contact.userId),
            })),
            identityDocs: employee.identityDocs?.map((doc) => ({
              ...doc,
              id: doc.id ? toNumber(doc.id) : undefined,
              userId: toNumber(doc.userId),
            })),
            bankAccounts: employee.bankAccounts?.map((account) => ({
              ...account,
              id: account.id ? toNumber(account.id) : undefined,
              userId: toNumber(account.userId),
            })),
          })) || [],
      }))
    );
  }

  updateEmployee(req: UpdateEmployeeRequest): Observable<EmployeeResponse> {
    return this.authService.updateEmployee(req).pipe(
      map((resp: EmployeeResponse) => ({
        ...resp,
        employee: resp.employee
          ? {
              ...resp.employee,
              userId: toNumber(resp.employee.userId),
              agencyId: resp.employee.agencyId
                ? toNumber(resp.employee.agencyId)
                : undefined,
              addresses: resp.employee.addresses?.map((addr) => ({
                ...addr,
                id: addr.id ? toNumber(addr.id) : undefined,
                userId: toNumber(addr.userId),
              })),
              emergencyContacts: resp.employee.emergencyContacts?.map(
                (contact) => ({
                  ...contact,
                  id: contact.id ? toNumber(contact.id) : undefined,
                  userId: toNumber(contact.userId),
                })
              ),
              identityDocs: resp.employee.identityDocs?.map((doc) => ({
                ...doc,
                id: doc.id ? toNumber(doc.id) : undefined,
                userId: toNumber(doc.userId),
              })),
              bankAccounts: resp.employee.bankAccounts?.map((account) => ({
                ...account,
                id: account.id ? toNumber(account.id) : undefined,
                userId: toNumber(account.userId),
              })),
            }
          : undefined,
      }))
    );
  }

  removeEmployee(
    req: RemoveEmployeeRequest
  ): Observable<RemoveEmployeeResponse> {
    return this.authService.removeEmployee(req);
  }
  // #endregion

  // ─── Organization-specific Role Management ─────────────────────────
  // #region
  createRole(req: CreateRoleRequest): Observable<RoleResponse> {
    return this.authService.createRole(req);
  }

  getRole(req: GetRoleRequest): Observable<RoleResponse> {
    return this.authService.getRole(req);
  }

  listRoles(req: ListRolesRequest): Observable<ListRolesResponse> {
    return this.authService.listRoles(req);
  }

  updateRoleNew(req: UpdateRoleRequest): Observable<RoleResponse> {
    return this.authService.updateRole(req);
  }

  deleteRoleNew(req: DeleteRoleRequest): Observable<DeleteRoleResponse> {
    return this.authService.deleteRole(req);
  }

  getOrganizationRoles(
    req: GetOrganizationRolesRequest
  ): Observable<GetOrganizationRolesResponse> {
    return this.authService.getOrganizationRoles(req);
  }
  // #endregion

  // ─── Organization-specific Department Management ─────────────────────────
  // #region
  createOrgDepartment(
    req: CreateDepartmentRequest
  ): Observable<OrgDepartmentResponse> {
    // Convert to bulk department creation format
    return this.authService.createDepartment(req).pipe(
      map((response) => ({
        success: response.success,
        message: response.message,
        department: response.department,
      }))
    );
  }

  getOrgDepartment(
    req: GetOrgDepartmentRequest
  ): Observable<OrgDepartmentResponse> {
    // This method is not implemented in the gRPC service
    return throwError(
      () =>
        new RpcException({
          code: status.UNIMPLEMENTED,
          message:
            'getDepartment method is not implemented in the gRPC service',
        })
    );
  }

  listOrgDepartments(
    req: ListOrgDepartmentsRequest
  ): Observable<ListOrgDepartmentsResponse> {
    // Use the existing GetOrganizationDepartments gRPC method
    // Default to hierarchy mode (true) unless explicitly set to false
    return this.authService
      .getOrganizationDepartments({
        organizationId: req.organizationId,
        includeHierarchy: req.includeChildren !== false, // Default to true
      })
      .pipe(
        map((response) => ({
          success: response.success,
          message: response.message,
          departments: serializeDepartments(response.departments || []),
          total: response.departments?.length || 0,
          page: req.page || 1,
          limit: req.limit || 10,
        }))
      );
  }

  updateOrgDepartment(
    req: UpdateOrgDepartmentRequest
  ): Observable<OrgDepartmentResponse> {
    // This method is not implemented in the gRPC service
    return throwError(
      () =>
        new RpcException({
          code: status.UNIMPLEMENTED,
          message:
            'updateDepartment method is not implemented in the gRPC service',
        })
    );
  }

  deleteOrgDepartment(
    req: DeleteOrgDepartmentRequest
  ): Observable<DeleteOrgDepartmentResponse> {
    return this.authService.deleteDepartment(req);
  }

  getOrganizationDepartments(
    req: GetOrganizationDepartmentsRequest
  ): Observable<GetOrganizationDepartmentsResponse> {
    return this.authService.getOrganizationDepartments(req).pipe(
      map((response) => ({
        success: response.success,
        message: response.message,
        departments: serializeDepartments(response.departments || []),
      }))
    );
  }

  renameOrgDepartment(
    req: RenameOrgDepartmentRequest
  ): Observable<RenameOrgDepartmentResponse> {
    return this.authService.renameOrgDepartment(req);
  }
  // #endregion

  // ─── Password Reset Methods ─────────────────────────
  // #region
  forgotPassword(
    req: ForgotPasswordRequest
  ): Observable<ForgotPasswordResponse> {
    return this.authService.forgotPassword(req);
  }

  verifyResetToken(
    req: VerifyResetTokenRequest
  ): Observable<VerifyResetTokenResponse> {
    return this.authService.verifyResetToken(req);
  }

  resetPassword(req: ResetPasswordRequest): Observable<ResetPasswordResponse> {
    return this.authService.resetPassword(req);
  }
  // #endregion
}
